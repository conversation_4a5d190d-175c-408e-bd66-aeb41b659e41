C/C++ Structured Logs
q
oC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\additional_project_files.txtC
A
?com.android.build.gradle.internal.cxx.io.EncodedFileFingerPrint	�����2 �����2p
n
lC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\android_gradle_build.json	�����2� �����2u
s
qC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\android_gradle_build_mini.json	�����2� �����2b
`
^C:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\build.ninja	�����2�� �����2f
d
bC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\build.ninja.txt	�����2k
i
gC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\build_file_index.txt	�����2
G �����2l
j
hC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\compile_commands.json	�����2p
n
lC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\compile_commands.json.bin	�����2	v
t
rC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\metadata_generation_command.txt	�����2
� �����2i
g
eC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\prefab_config.json	�����2
( �����2n
l
jC:\KARGALAR\Projeler\Flutter\gamify_todo\android\app\.cxx\Debug\6a3y14v3\arm64-v8a\symbol_folder_index.txt	�����2
a �����2K
I
GC:\flutter\packages\flutter_tools\gradle\src\main\groovy\CMakeLists.txt	�����2
� ����2